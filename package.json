{"name": "ee-acc-general-gateway", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --runInBand", "test:watch": "jest --watch", "test:cov": "jest --coverage --runInBand", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@azure/identity": "^4.11.0", "@faker-js/faker": "9.3.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.0", "@nestjs/microservices": "^10.3.8", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.3.8", "@nestjs/schedule": "^4.1.1", "@typegoose/typegoose": "^12.4.0", "@types/multer": "^1.4.12", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "bcrypt": "^5.1.1", "consola": "^3.2.3", "core-js": "^3.37.1", "csv-stringify": "^6.5.2", "csvtojson": "^2.0.10", "dayjs": "^1.11.11", "hbs": "^4.2.0", "http2-wrapper": "^2.2.1", "mongodb": "^6.8.0", "mongoose": "^8.9.0", "mongoose-aggregate-paginate-v2": "^1.0.7", "mongoose-paginate-v2": "^1.8.0", "nanoid": "^3.0.0", "nestjs-zod": "^3.0.0", "nodemailer": "^6.9.15", "p-limit": "3.1.0", "pidusage": "^4.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "ts-patch-mongoose": "^2.9.0", "xlsx": "^0.18.5", "zod": "^3.23.8", "zx-cjs": "^7.0.7-0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.4", "@types/mongoose-aggregate-paginate-v2": "^1.0.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.15", "@types/pidusage": "^2.0.5", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-gitignore": "^0.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^12.1.0", "jest": "^29.5.0", "jest-ctrf-json-reporter": "^0.0.9", "jest-html-reporters": "^3.1.7", "jest-junit": "^16.0.0", "mongodb-memory-server": "^10.1.4", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}}
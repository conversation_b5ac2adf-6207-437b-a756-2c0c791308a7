import {
  BadRequestException,
  Controller,
  NotFoundException,
} from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { ContractType } from '~/shared/enums/contract.enum';
import { CONTRACT_MESSAGE_KEYS } from '~/shared/message-keys/contract.message-keys';
import { CONTRACT_MESSAGES } from '~/shared/messages/contract.message';
import { zodParseOrThrow } from '~/utils';

import { ContractService } from './contract.service';
import {
  CreateContractDto,
  CreateCostCenterServiceContractSchema,
  CreateCreditorContractSchema,
  CreateLocationServiceContractSchema,
  CreateRentingContractSchema,
  CreateSupplierContractSchema,
} from './dtos/create-contract.dto';

@Controller('contracts')
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: CONTRACT_MESSAGES.GET_CONTRACTS })
  public async findAll(@Payload() payload: any) {
    return this.contractService.findAll(payload);
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.DETAIL_CONTRACT })
  public async findOne(@Payload() payload: any) {
    const result = await this.contractService.findDetailOfOne(payload);
    if (!result) {
      throw new NotFoundException(CONTRACT_MESSAGE_KEYS.NOT_FOUND);
    }

    return result;
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.SUPPLIER_CONTRACT_LOCATION })
  public async findLocationOfContract(@Payload() payload: any) {
    return this.contractService.findLocationOfContract(payload);
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.CREATE_CONTRACT })
  public async create(@Payload() payload: CreateContractDto) {
    switch (payload.type) {
      case ContractType.RENTING:
        payload = zodParseOrThrow(CreateRentingContractSchema, payload);
        break;
      case ContractType.SERVICE:
        if (payload.location) {
          payload = zodParseOrThrow(
            CreateLocationServiceContractSchema,
            payload,
          );
        } else {
          payload = zodParseOrThrow(
            CreateCostCenterServiceContractSchema,
            payload,
          );
        }
        break;
      case ContractType.CREDITOR:
        payload = zodParseOrThrow(CreateCreditorContractSchema, payload);
        break;
      case ContractType.SUPPLIER:
        payload = zodParseOrThrow(CreateSupplierContractSchema, payload);
        break;
      default:
        throw new BadRequestException(
          CONTRACT_MESSAGE_KEYS.INVALID_CONTRACT_TYPE,
        );
    }

    return this.contractService.create(payload);
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.UPDATE_CONTRACT })
  public async update(@Payload() payload: any) {
    return this.contractService.update(payload);
  }
}

import { isValidObjectId } from 'mongoose';
import { z, ZodObject, ZodRawShape } from 'nestjs-zod/z';

export interface CreateCostLineGeneralDto {
  description: string;
  price?: number;
  position?: number;
  unit?: string;
  startDate: string;
  endDate?: string | null;
  costType?: string;
}

export const CreateRentingCostLineGeneralSchema = validateEndDate(
  z.strictObject({
    description: z.string().min(1).max(256),
    price: z.number(),
    position: z.number().int().min(0).optional(),
    unit: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    startDate: z.dateString(),
    endDate: z.dateString().nullish().optional(),
    costType: z
      .string()
      .trim()
      .optional()
      .refine((val) => !val || isValidObjectId(val)),
  }),
);

export const CreateServiceCostLineGeneralSchema = validateEndDate(
  z.strictObject({
    description: z.string().min(1).max(256),
    price: z.number().optional(),
    position: z.number().int().min(0).optional(),
    startDate: z.dateString(),
    endDate: z.dateString().nullish(),
    unit: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    costType: z
      .string()
      .trim()
      .optional()
      .refine((val) => !val || isValidObjectId(val)),
  }),
);

export const CreateSupplierCostLineGeneralSchema = validateEndDate(
  z.strictObject({
    description: z.string().min(1).max(256),
    price: z.number().optional(),
    position: z.number().int().min(0).optional(),
    startDate: z.dateString(),
    endDate: z.dateString().nullish(),
    costType: z
      .string()
      .trim()
      .optional()
      .refine((val) => !val || isValidObjectId(val)),
  }),
);

function validateEndDate<T extends ZodRawShape>(schema: ZodObject<T>) {
  return schema.refine(
    (schema) =>
      !schema.endDate ||
      Date.parse(schema.startDate) < Date.parse(schema.endDate),
    {
      message: 'End date must be greater than start date',
      path: ['endDate'],
    },
  );
}

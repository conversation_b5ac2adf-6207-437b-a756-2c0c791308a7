import { Injectable } from '@nestjs/common';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { TenantRoleModel } from './tenant-role.model';

@Injectable()
export class TenantRoleService {
  constructor(
    @InjectModel(TenantRoleModel)
    private readonly tenantRoleModel: MongooseModel<TenantRoleModel>,
  ) {}

  async findAll(payload: any) {
    const { query, ...rest } = buildQuery(payload, ['name', 'key']);
    let { offset, limit } = rest.options;
    const queryCondition = {
      ...query,
      key: { $ne: 'administrator' },
      isActive: true,
    };
    if (payload.pageSize == -1) {
      offset = 0;
      limit = await this.tenantRoleModel.countDocuments(queryCondition).lean();
    }
    return this.tenantRoleModel.paginate(
      {
        ...queryCondition,
      },
      {
        ...rest.options,
        offset,
        limit,
        select: 'key name',
      },
    );
  }
}

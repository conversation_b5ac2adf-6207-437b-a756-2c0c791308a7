{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript"], "chat.mcp.serverSampling": {"ee-acc-v2-core/.vscode/mcp.json: GetTicketOnJira": {"allowedModels": ["github.copilot-chat/gpt-4.1", "github.copilot-chat/claude-3.5-sonnet", "github.copilot-chat/claude-3.7-sonnet", "github.copilot-chat/claude-3.7-sonnet-thought", "github.copilot-chat/gemini-2.0-flash-001", "github.copilot-chat/gemini-2.5-pro", "github.copilot-chat/gpt-4o", "github.copilot-chat/o3-mini", "github.copilot-chat/o4-mini"]}}}
import { ClientSecretCredential } from '@azure/identity';
import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import _, { template } from 'lodash';
import { Model } from 'mongoose';
import { createTransport, Transporter } from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';

import { EmailTemplateService } from '~/modules/email-template/email-template.service';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { InjectModel } from '~/transformers/model.transformer';
import { renderTextTemplate } from '~/utils/render-text-template.util';

import { MyLogger } from '../logger/logger.service';
import { SendEmailDto, SendErrorEmailToLentoDto } from './dtos/send-email.dto';

@Injectable()
export class EmailService implements OnModuleDestroy, OnModuleInit {
  private transporter!: Transporter;
  private credential!: ClientSecretCredential;
  private tokenExpiryTime: number = 0;
  private refreshTokenInterval: NodeJS.Timeout | null = null;

  constructor(
    @InjectModel(TenantModel)
    private readonly tenantModel: Model<TenantModel>,
    private readonly configService: ConfigService,
    private readonly logger: MyLogger,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}

  async onModuleInit() {
    await this.initializeOffice365SMTP();
  }

  private async initializeOffice365SMTP() {
    try {
      // Initialize Azure credential
      this.credential = new ClientSecretCredential(
        this.configService.get<string>('email.office365.tenantId')!,
        this.configService.get<string>('email.office365.clientId')!,
        this.configService.get<string>('email.office365.clientSecret')!,
      );

      // Get initial access token
      await this.refreshAccessToken();

      // Set up automatic token refresh
      this.setupTokenRefresh();
    } catch (error: any) {
      this.logger.error('Failed to initialize Office 365 SMTP client:', error);
    }
  }

  private async setupTokenRefresh() {
    // Clear existing interval if any
    if (this.refreshTokenInterval) {
      clearInterval(this.refreshTokenInterval);
    }

    // Calculate time until token expires (refresh 5 minutes before expiry)
    const now = Date.now();
    const timeUntilExpiry = this.tokenExpiryTime - now - 5 * 60 * 1000; // 5 minutes buffer

    if (timeUntilExpiry > 0) {
      // Set interval to refresh token before it expires
      this.refreshTokenInterval = setTimeout(async () => {
        this.logger.log('Refreshing access token before expiry...');
        await this.refreshAccessToken();
        this.setupTokenRefresh(); // Set up next refresh
      }, timeUntilExpiry);
    } else {
      // Token is already expired or will expire soon, refresh immediately
      this.logger.warn(
        'Token is expired or will expire soon, refreshing immediately...',
      );
      await this.refreshAccessToken();
      this.setupTokenRefresh(); // Set up next refresh
    }
  }

  async sendEmailWithTemplate(templateName: string, data: object) {
    const template =
      (await this.emailTemplateService.getEmailTemplateByName(templateName))!;

    const escapedData = _.mapValues(data, (value) => _.escape(value));

    const mailOptions = {
      from: this.configService.get<string>('email.office365.userId')!,
      to: template.to?.join(', ') ?? '',
      cc: template.cc?.join(', ') ?? '',
      bcc: template.bcc?.join(', ') ?? '',
      subject: renderTextTemplate(data, template.subject),
      html: renderTextTemplate(escapedData, template.html),
      text: renderTextTemplate(escapedData, template.text ?? template.html),
    };

    return this.sendEmailWithRetry(mailOptions);
  }

  async sendEmail(params: SendEmailDto) {
    const mailOptions = {
      from: this.configService.get<string>('email.office365.userId')!,
      to: Array.isArray(params.to) ? params.to.join(', ') : params.to,
      cc: params.cc?.join(', ') ?? '',
      bcc: params.bcc?.join(', ') ?? '',
      subject: params.subject,
      html: params.html ?? '',
      text: params.text ?? '',
    };

    return this.sendEmailWithRetry(mailOptions);
  }

  private async sendEmailWithRetry(mailOptions: any) {
    try {
      const response = await this.transporter.sendMail(mailOptions);
      this.logger.log('Email sent successfully via Office 365 SMTP');
      return response;
    } catch (error: any) {
      // Check if it's an authentication error
      if (
        error.code === 'EAUTH' ||
        error.message?.includes('Authentication unsuccessful') ||
        error.message?.includes('Invalid token')
      ) {
        this.logger.warn(
          'Authentication failed, attempting to refresh token...',
        );
        try {
          await this.refreshAccessToken();
          // Retry sending email with new transporter
          const retryResponse = await this.transporter.sendMail(mailOptions);
          this.logger.log('Email sent successfully after token refresh');
          return retryResponse;
        } catch (retryError: any) {
          this.logger.error(
            'Failed to send email after token refresh:',
            retryError,
          );
          throw retryError;
        }
      }

      this.logger.error('Failed to send email via Office 365 SMTP:', error);
      throw error;
    }
  }

  async sendErrorEmailToLento(data: SendErrorEmailToLentoDto) {
    const emailTemplate =
      await this.emailTemplateService.getEmailTemplateByName(
        'error_email_send_to_lento',
      );

    if (!emailTemplate) {
      this.logger.error(`Email template not found: error_email_send_to_lento`);
      return;
    }

    const receiverEmail = this.configService.get<string>(
      'app.lentoErrorReceiverEmail',
    );

    if (!receiverEmail) {
      this.logger.error(`Lento error receiver email is not configured`);
      return;
    }

    const tenant = await this.tenantModel.findOne({}).lean();
    if (!tenant) {
      this.logger.error(`Tenant not found`);
      return;
    }

    data.TENANT = tenant.name;

    const html = emailTemplate.html;
    const compiledObject = template(html);
    const htmlTemplate = compiledObject(data);

    await this.sendEmail({
      html: htmlTemplate,
      text: htmlTemplate,
      to: [receiverEmail],
      subject: emailTemplate.subject,
      bcc: emailTemplate.bcc,
      cc: emailTemplate.cc,
    });
  }

  // Method to refresh access token
  private async refreshAccessToken() {
    try {
      const token = await this.credential.getToken([
        'https://outlook.office365.com/.default',
      ]);

      // Store token expiry time
      this.tokenExpiryTime = token.expiresOnTimestamp!;

      // Initialize SMTP transporter with OAuth2
      const options: SMTPTransport.Options = {
        host: 'smtp.office365.com',
        port: this.configService.get<number>('email.office365.port')!,
        // NOSONAR - STARTTLS requires secure: false
        secure: this.configService.get<boolean>('email.office365.isSecure')!, // NOSONAR
        // port: 465,
        // secure: true,
        auth: {
          type: 'OAuth2',
          user: this.configService.get<string>('email.office365.userId')!,
          clientId: this.configService.get<string>('email.office365.clientId')!,
          clientSecret: this.configService.get<string>(
            'email.office365.clientSecret',
          )!,
          accessToken: token.token,
        },
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: true,
        },
        debug: false, // Disable debug logging for production
        logger: false, // Disable logger for production
      };

      this.transporter = createTransport(options, {
        from: {
          name: 'No-reply',
          address: this.configService.get<string>('email.office365.userId')!,
        },
      });

      // Verify connection
      this.transporter.verify((error) => {
        if (error) {
          this.logger.error(
            'SMTP connection verification failed:',
            error.message,
          );
        } else {
          this.logger.log('Office 365 SMTP client initialized successfully');
        }
      });

      this.logger.log('Access token refreshed successfully');
      return token.token;
    } catch (error: any) {
      this.logger.error('Failed to refresh access token:', error);
      throw error;
    }
  }

  // Method to get user's email address
  async getUserEmail() {
    try {
      return this.configService.get<string>('email.office365.userId');
    } catch (error: any) {
      this.logger.error('Failed to get user email:', error);
      throw error;
    }
  }

  // Method to cleanup resources
  onModuleDestroy() {
    if (this.refreshTokenInterval) {
      clearTimeout(this.refreshTokenInterval);
      this.refreshTokenInterval = null;
    }
    this.logger.log('Email service cleanup completed');
  }
}

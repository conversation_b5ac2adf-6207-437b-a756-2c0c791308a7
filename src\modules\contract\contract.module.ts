import { Module } from '@nestjs/common';

import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { CostlineModule } from '~/modules/costline/costline.module';
import { CoreEventEmitterModule } from '~/processors/event-emitter/event-emitter.module';

import { TenantUserModule } from '../tenant-user/tenant-user.module';
import { ContractController } from './contract.controller';
import { ContractService } from './contract.service';

@Module({
  imports: [
    CostlineModule,
    TenantUserModule,
    CoreEventEmitterModule,
    CostCenterModel,
  ],
  controllers: [ContractController],
  providers: [ContractService],
  exports: [ContractService],
})
export class ContractModule {}

import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { mongoose } from '@typegoose/typegoose';
import dayjs from 'dayjs';
import { difference } from 'lodash';
import { PipelineStage, Types } from 'mongoose';
import { ZodError } from 'nestjs-zod/z';

import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { CostlineService } from '~/modules/costline/costline.service';
import { EventEmitterSender } from '~/processors/event-emitter/event-emitter.sender';
import { ContactRole, SupplierType } from '~/shared/enums/contact.enum';
import {
  AgreementLinePeriod,
  AgreementLineType,
  ContractType,
  OwnerType,
} from '~/shared/enums/contract.enum';
import { IdentifierType } from '~/shared/enums/identifier.enum';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { ModuleNameEnum } from '~/shared/enums/module-name.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { CONTRACT_MESSAGE_KEYS } from '~/shared/message-keys/contract.message-keys';
import { CONTRACT_TYPE_MESSAGE_KEYS } from '~/shared/message-keys/contract-type.message-key';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';
import {
  getManageRoleByContractType,
  getReadRolesByContractType,
} from '~/utils/contractRoles.util';
import { parseRangeDates } from '~/utils/date.util';
import { validteAndSortPosition } from '~/utils/position.util';

import { AgreementLineModel } from '../agreementline/agreementline.model';
import {
  CreateAgreementLineDto,
  CreateRentingAgreementLineSchema,
  CreateServiceAgreementLineSchema,
  CreateSupplierAgreementLineSchema,
} from '../agreementline/dtos/create-agreementline.dto';
import {
  UpdateRentingAgreementLineSchema,
  UpdateServiceAgreementLineSchema,
  UpdateSupplierAgreementLineSchema,
} from '../agreementline/dtos/update-agreementline.dto';
import { ContactModel } from '../contact/contact.model';
import { ContractTypeModel } from '../contract-type/contract-type.model';
import { CostLineGeneralModel } from '../costlinegeneral/costlinegeneral.model';
import {
  CreateRentingCostLineGeneralSchema,
  CreateServiceCostLineGeneralSchema,
  CreateSupplierCostLineGeneralSchema,
} from '../costlinegeneral/dtos/create-costlinegeneral.dto';
import { UpdateCostLineGeneralSchema } from '../costlinegeneral/dtos/update-costlinegeneral.dto';
import { IdentifierService } from '../identifier/identifier.service';
import { LocationModel } from '../location/location.model';
import { TenantUserService } from '../tenant-user/tenant-user.service';
import { UnitModel } from '../unit/unit.model';
import {
  calculate2PeriodsFutureGenerationDate,
  calculateNextFutureGenerationDate,
  generateRangeDatesMatchConditions,
  getCostLineGeneralUpdatedFields,
  getFutureGenerationDate,
  getGreatestAgreementLinePeriod,
} from './contract.helper';
import { ContractModel } from './contract.model';
import {
  CreditorContractQueryParamsSchema,
  RentingContractQueryParamsSchema,
  ServiceContractQueryParamsSchema,
  SupplierContractQueryParamsSchema,
} from './dtos/contract.dto';
import { CreateContractDto } from './dtos/create-contract.dto';
import {
  UpdateCreditorRentingContractSchema,
  UpdateDebtorRentingContractSchema,
  UpdateDebtorServiceContractSchema,
  UpdateSupplierContractSchema,
} from './dtos/update-contract.dto';

@Injectable()
export class ContractService {
  constructor(
    @InjectModel(ContractModel)
    private readonly contractModel: MongooseModel<ContractModel>,
    @InjectModel(AgreementLineModel)
    private readonly agreementLineModel: MongooseModel<AgreementLineModel>,
    @InjectModel(CostLineGeneralModel)
    private readonly costLineGeneralModel: MongooseModel<CostLineGeneralModel>,
    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: MongooseModel<LocationModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: MongooseModel<UnitModel>,
    @InjectModel(ContractTypeModel)
    private readonly contractTypeModel: MongooseModel<ContractTypeModel>,
    @InjectModel(CostCenterModel)
    private readonly costCenterModel: MongooseModel<CostCenterModel>,

    private readonly identifierService: IdentifierService,
    private readonly costLineService: CostlineService,
    private readonly tenantUserService: TenantUserService,
    private readonly eventEmitterSender: EventEmitterSender,
  ) {}

  async findAll(payload: any) {
    await this.validateReadPermissionByContractType(payload.user, payload.type);

    // unset user from payload after validate permission
    delete payload.user;

    this.validatePayloadWhenFindAllContract(payload);
    const {
      startDateFrom,
      startDateTo,
      type,
      contractTypeName,
      owner,
      ...rest
    } = payload;

    if (owner === OwnerType.HOMEE.toLowerCase()) {
      rest.isGenerateCostLine = true;
    } else if (owner === OwnerType.LENTO.toLowerCase()) {
      rest.isGenerateCostLine = false;
    }

    const queryOptions = buildQuery(rest, []);
    const { query } = queryOptions;
    const { options } = queryOptions;

    const { djsStartDate, djsEndDate } = parseRangeDates(
      startDateFrom,
      startDateTo,
    );
    const matchConditions = generateRangeDatesMatchConditions(
      djsStartDate,
      djsEndDate,
    );

    if (contractTypeName) {
      const contractType = await this.contractTypeModel.findOne({
        name: contractTypeName,
      });

      query.contractType = contractType?._id;
    }

    const baseAggregateArray = [
      {
        $match: {
          ...(matchConditions.length > 0 ? { $or: matchConditions } : {}),
        },
      },
      {
        $match: {
          type: type,
          ...query,
        },
      },
    ];

    let aggregateArray: PipelineStage[] = [...baseAggregateArray];

    let lookupFields: any[] = [];
    let projecFields: any = {};

    switch (payload.type) {
      case ContractType.RENTING:
      case ContractType.CREDITOR: {
        lookupFields = [
          {
            $lookup: {
              from: 'locations',
              localField: 'location',
              foreignField: '_id',
              as: 'location',
            },
          },
          {
            $unwind: {
              path: '$location',
              preserveNullAndEmptyArrays: true,
            },
          },
        ];
        projecFields = {
          'location._id': '$location._id',
          'location.fullAddress': '$location.fullAddress',
        };
        break;
      }
      case ContractType.SERVICE: {
        lookupFields = [
          {
            $lookup: {
              from: 'costcenters',
              localField: 'costCenter',
              foreignField: '_id',
              as: 'costCenter',
            },
          },
          {
            $unwind: {
              path: '$costCenter',
              preserveNullAndEmptyArrays: true,
            },
          },
        ];
        projecFields = {
          'costCenter._id': '$costCenter._id',
          'costCenter.displayName': {
            $concat: ['$costCenter.identifier', ' - ', '$costCenter.name'],
          },
        };
        break;
      }
      case ContractType.SUPPLIER: {
        lookupFields = [
          {
            $lookup: {
              from: 'contracttypes',
              localField: 'contractType',
              foreignField: '_id',
              as: 'contractType',
            },
          },
          {
            $unwind: {
              path: '$contractType',
              preserveNullAndEmptyArrays: true,
            },
          },
        ];
        projecFields = {
          'contractType._id': '$contractType._id',
          'contractType.name': '$contractType.name',
        };
        break;
      }
      case ContractType.CUSTOM: {
        break;
      }
      default:
        break;
    }

    aggregateArray = [
      ...aggregateArray,
      {
        $lookup: {
          from: 'contacts',
          localField: 'contact',
          foreignField: '_id',
          as: 'contact',
        },
      },
      {
        $unwind: {
          path: '$contact',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...lookupFields,
      {
        $project: {
          _id: 1,
          identifier: 1,
          'contact._id': '$contact._id',
          'contact.displayName': '$contact.displayName',
          ...projecFields,
          startDate: 1,
          endDate: 1,
          updatedAt: 1,
          owner: {
            $cond: {
              if: { $eq: ['$type', ContractType.RENTING] },
              then: {
                $cond: {
                  if: { $eq: ['$isGenerateCostLine', true] },
                  then: OwnerType.HOMEE,
                  else: OwnerType.LENTO,
                },
              },
              else: '$$REMOVE',
            },
          },
        },
      },
      {
        $sort: options.sort,
      },
    ];

    return this.contractModel.aggregatePaginate(
      this.contractModel.aggregate(aggregateArray, {
        collation: options.collation,
      }),
      {
        offset: options.offset,
        limit: options.limit,
        allowDiskUse: true,
      },
    );
  }

  async findDetailOfOne(id: string) {
    const result = await this.contractModel
      .aggregate()
      .match({ _id: new mongoose.Types.ObjectId(id) })
      .lookup({
        from: 'agreementlines',
        localField: 'agreementLines',
        foreignField: '_id',
        let: { isNew: '$isNew' },
        as: 'agreementLines',
        pipeline: [
          {
            $lookup: {
              from: 'units',
              localField: 'units',
              foreignField: '_id',
              as: 'units',
              pipeline: [
                {
                  $project: {
                    name: 1,
                    isRoot: 1,
                    parent: 1,
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: 'costlinegenerals',
              localField: 'costLineGenerals',
              foreignField: '_id',
              as: 'costLineGenerals',
              let: { period: '$period', isNew: '$$isNew' },
              pipeline: [
                {
                  $lookup: {
                    from: 'costlines',
                    localField: '_id',
                    foreignField: 'costLineGeneral',
                    as: 'costlines',
                  },
                },
                {
                  $addFields: {
                    hasApproved: {
                      $cond: {
                        if: {
                          $gt: [
                            {
                              $size: {
                                $filter: {
                                  input: '$costlines',
                                  as: 'costline',
                                  cond: {
                                    $ifNull: ['$$costline.approvedAt', false],
                                  },
                                },
                              },
                            },
                            0,
                          ],
                        },
                        then: true,
                        else: false,
                      },
                    },
                  },
                },
                {
                  $lookup: {
                    from: 'costtypes',
                    localField: 'costType',
                    foreignField: '_id',
                    as: 'costType',
                  },
                },
                {
                  $addFields: {
                    costType: {
                      $arrayElemAt: ['$costType', 0],
                    },
                    amount: {
                      $switch: {
                        branches: [
                          {
                            case: {
                              $eq: ['$$period', AgreementLinePeriod.WEEKLY],
                            },
                            then: '$price',
                          },
                          {
                            case: {
                              $eq: [
                                '$$period',
                                AgreementLinePeriod.FOUR_WEEKLY,
                              ],
                            },
                            then: { $multiply: ['$price', 4] },
                          },
                          {
                            case: {
                              $eq: ['$$period', AgreementLinePeriod.MONTHLY],
                            },
                            then: {
                              $cond: {
                                if: { $ifNull: ['$$isNew', true] },
                                then: {
                                  $multiply: [
                                    { $divide: ['$price', 7.0] },
                                    { $divide: [365.0, 12.0] },
                                  ],
                                },
                                else: {
                                  $multiply: ['$price', 4.333333],
                                },
                              },
                            },
                          },
                        ],
                        default: '$price',
                      },
                    },
                  },
                },
                { $project: { costlines: 0 } },
              ],
            },
          },
        ],
      })
      .lookup({
        from: 'contacts',
        localField: 'contact',
        foreignField: '_id',
        as: 'contact',
        pipeline: [
          {
            $project: {
              displayName: 1,
              organizationNames: 1,
            },
          },
        ],
      })
      .lookup({
        from: 'contracttypes',
        localField: 'contractType',
        foreignField: '_id',
        as: 'contractType',
      })
      .lookup({
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        as: 'location',
        pipeline: [
          {
            $lookup: {
              from: 'addresses',
              localField: 'address',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    street: 1,
                    city: 1,
                    number: 1,
                    suffix: 1,
                    postalCode: 1,
                  },
                },
              ],
              as: 'address',
            },
          },
          {
            $project: {
              fullAddress: 1,
              address: {
                $arrayElemAt: ['$address', 0],
              },
            },
          },
        ],
      })
      .lookup({
        from: 'costcenters',
        localField: 'costCenter',
        foreignField: '_id',
        as: 'costCenter',
      })
      .addFields({
        contact: {
          $arrayElemAt: ['$contact', 0],
        },
        contractType: {
          $arrayElemAt: ['$contractType', 0],
        },
        location: {
          $arrayElemAt: ['$location', 0],
        },
        costCenter: {
          $arrayElemAt: ['$costCenter', 0],
        },
      })
      .exec();

    return result.length ? result[0] : null;
  }

  async findLocationOfContract(id: string) {
    return (
      await this.contractModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(id),
            type: ContractType.SUPPLIER,
          },
        },
        {
          $lookup: {
            from: 'locationadditionals',
            localField: '_id',
            foreignField: 'contract',
            pipeline: [
              {
                $match: {
                  type: {
                    $in: [
                      LocationAdditionalType.FEATURE_AND_SUPPLIER,
                      LocationAdditionalType.GWE_AND_METER_READING,
                    ],
                  },
                  isDeleted: false,
                },
              },
            ],
            as: 'locationadditionals',
          },
        },
        {
          $lookup: {
            from: 'locations',
            localField: 'locationadditionals.location',
            foreignField: '_id',
            pipeline: [
              {
                $addFields: { fullAddressLower: { $toLower: '$fullAddress' } },
              },
              { $sort: { fullAddressLower: 1 } },
            ],
            as: 'locations',
          },
        },
        {
          $addFields: { maxOccupants: { $sum: '$locations.maxOccupants' } },
        },
        { $project: { _id: 1, type: 1, locations: 1, maxOccupants: 1 } },
      ])
    )[0];
  }

  async create(data: CreateContractDto) {
    const { user } = data;
    if (!user) {
      throw new ForbiddenException();
    }

    await this.validateManagePermissionByContractType(user, data.type);
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const { agreementLines, ...contractData } = data;

      contractData.identifier =
        (await this.identifierService.generateIdentifier(
          IdentifierType.CONTRACT,
        ))!;

      const [contact, isValidLocation, isValidContractType] = await Promise.all(
        [
          this.contactModel.findOne({ _id: contractData.contact }).lean(),
          !!contractData.location
            ? this.locationModel.exists({ _id: contractData.location })
            : true,
          !!contractData.contractType
            ? this.contractTypeModel.exists({ _id: contractData.contractType })
            : true,
        ],
      );

      if (!contact) {
        throw new NotFoundException(
          CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
        );
      } else {
        this.validateContactType(contractData.type, contact as any);
      }

      if (!isValidLocation) {
        throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
      }

      if (!isValidContractType) {
        throw new NotFoundException(CONTRACT_TYPE_MESSAGE_KEYS.NOT_FOUND);
      }

      if (!contractData.isWholeLocation) {
        await this.validateUnitsInAgreementLines(
          contractData.location!,
          agreementLines,
        );
      } else {
        await this.overwriteRootUnit(data);
      }
      const dayJsCurrentDate = dayjs().utc().startOf('day');
      if (contractData.endDate) {
        const dayJsEndDate = dayjs(contractData.endDate).utc().startOf('day');
        if (dayJsEndDate.isBefore(dayJsCurrentDate)) {
          contractData.isActive = false;
        }
      }

      if (contractData.type === ContractType.SERVICE && contractData.location) {
        const costCenter = await this.costCenterModel.findOne({
          locations: new Types.ObjectId(contractData.location),
        });

        contractData.costCenter = costCenter?._id;
      }

      // Create contract
      const [contract] = await this.contractModel.create([contractData], {
        session,
        ordered: true,
      });

      const greatestAgreementLineStatus = getGreatestAgreementLinePeriod(
        agreementLines,
        dayJsCurrentDate,
      );

      // Create agreement lines and cost line generals
      const agreementLinePromises = agreementLines.map(async (line: any) => {
        const { costLineGenerals, ...agreementLineData } = line;

        // Add reference to contract
        agreementLineData.contract = contract._id;

        const [agreementLine] = await this.agreementLineModel.create(
          [agreementLineData],
          { session, ordered: true },
        );

        // Create cost line generals with reference to agreement line
        const futureGenerationDate = greatestAgreementLineStatus.isFuture
          ? calculate2PeriodsFutureGenerationDate({
              greatestPeriodType:
                greatestAgreementLineStatus.greatestPeriodType,
              startDate: greatestAgreementLineStatus.smallestStartDate,
              now: dayJsCurrentDate,
            })
          : undefined;
        const noFutureGenerationDate =
          !futureGenerationDate ||
          dayJsCurrentDate.isSameOrAfter(futureGenerationDate);

        const costLineGeneralsData = costLineGenerals.map(
          (costLineGeneral: any) => ({
            ...costLineGeneral,
            agreementLine: agreementLine._id,
            endDate: costLineGeneral.endDate || contract.endDate,
            futureGenerationDate: noFutureGenerationDate
              ? calculateNextFutureGenerationDate({
                  period: agreementLine.period,
                  generatePeriod: contract.generatePeriod,
                  startDate: dayjs(costLineGeneral.startDate),
                  now: dayJsCurrentDate,
                })
              : futureGenerationDate,
          }),
        );

        const createdCostLineGenerals = await this.costLineGeneralModel.create(
          costLineGeneralsData,
          {
            session,
            ordered: true,
          },
        );

        // Update reference of agreement line
        await this.agreementLineModel.findByIdAndUpdate(
          agreementLine._id,
          {
            costLineGenerals: createdCostLineGenerals.map(
              (costLine) => costLine._id,
            ),
          },
          { session },
        );

        let costLines: any[];
        if (
          contract.isGenerateCostLine &&
          (!futureGenerationDate ||
            dayJsCurrentDate.isSameOrAfter(futureGenerationDate))
        ) {
          const costLinesPromises = createdCostLineGenerals.map(
            async (costLineGeneral) => {
              return await this.costLineService.createCostLines({
                costLineGeneral,
                agreementLine,
                contract,
                session,
              });
            },
          );

          costLines = await Promise.all(costLinesPromises);
        } else {
          costLines = [];
        }

        return {
          agreementLine,
          costLineGenerals: createdCostLineGenerals,
          costLines: costLines.flat(),
        };
      });

      const results = await Promise.all(agreementLinePromises);

      await session.commitTransaction();
      const createdContract = await this.contractModel
        .findByIdAndUpdate(
          contract._id,
          {
            agreementLines: results.map((result) => result.agreementLine._id),
          },
          { new: true },
        )
        .populate([
          {
            path: 'agreementLines',
            populate: ['costLineGenerals'],
          },
        ]);
      await this.eventEmitterSender.createContractEvent(createdContract);
      return createdContract;
    } catch (error) {
      await session.abortTransaction();
      await session.endSession();
      throw error;
    } finally {
      await session.endSession();
    }
  }

  async update(payload: any) {
    const { id, user } = payload;
    if (!user) {
      throw new ForbiddenException();
    }

    const contract = await this.contractModel.findById(id).lean();
    if (!contract) {
      throw new BadRequestException(CONTRACT_MESSAGE_KEYS.NOT_FOUND);
    }
    await this.validateManagePermissionByContractType(user, contract.type);

    const data = this.validateDataWhenUpdateContract(contract.type, payload);
    // when endDate	< current date, isActive = false
    const dayJsCurrentDate = dayjs().utc().startOf('day');
    if (data.endDate) {
      const dayJsEndDate = dayjs(data.endDate).utc().startOf('day');
      if (dayJsEndDate.isBefore(dayJsCurrentDate)) {
        data.isActive = false;
      }
    }

    if (data.startDate) {
      const dayJsStartDate = dayjs(data.startDate).utc().startOf('day');
      const dayJsStartDateContract = dayjs(contract.startDate)
        .utc()
        .startOf('day');
      if (!dayJsStartDate.isSame(dayJsStartDateContract)) {
        const acceptStartDate = dayjs()
          .utc()
          .subtract(60, 'days')
          .startOf('day');
        if (dayJsStartDate.isBefore(acceptStartDate)) {
          throw new BadRequestException(
            CONTRACT_MESSAGE_KEYS.START_DATE_CANNOT_BE_CHANGED_BEFORE_60_DAYS,
          );
        }
      }
    }

    const { agreementLines, ...rest } = data;
    const pushAgreementLineIds: any[] = [];
    rest._id = contract._id;
    if (agreementLines && agreementLines.length > 0) {
      const { createdAgreementLineIds } = await this.updateAgreementLine(
        contract,
        rest,
        agreementLines,
      );
      pushAgreementLineIds.push(...createdAgreementLineIds);
    }

    const updatedContract = await this.contractModel
      .findByIdAndUpdate(
        id,
        {
          ...rest,
          $push: {
            agreementLines: {
              $each: pushAgreementLineIds,
            },
          },
        },
        { new: true },
      )
      .exec();
    await this.eventEmitterSender.updateContractEvent(updatedContract);
    return await this.findDetailOfOne(id);
  }

  async deactiveContractByCronJob() {
    await this.contractModel.updateMany(
      {
        isActive: true,
        isDeleted: false,
        endDate: { $lt: dayjs().utc().startOf('day').toDate() },
      },
      { isActive: false },
    );
  }

  //#region

  private validatePayloadWhenFindAllContract(payload: any) {
    try {
      switch (payload.type) {
        case ContractType.RENTING:
          return RentingContractQueryParamsSchema.parse(payload);
        case ContractType.CREDITOR: {
          return CreditorContractQueryParamsSchema.parse(payload);
        }
        case ContractType.SERVICE: {
          return ServiceContractQueryParamsSchema.parse(payload);
        }
        case ContractType.CUSTOM: {
          break;
        }
        case ContractType.SUPPLIER: {
          return SupplierContractQueryParamsSchema.parse(payload);
        }
        default:
          break;
      }
    } catch (error: any) {
      if (error instanceof ZodError) {
        const errorPayload = {
          message: 'Validation failed',
          errors: error.errors,
        };
        throw new BadRequestException(errorPayload);
      } else {
        throw error;
      }
    }
  }

  private async validateUnitsInAgreementLines(
    location: string | Types.ObjectId,
    agreementLines: CreateAgreementLineDto[],
  ) {
    await Promise.all(
      agreementLines
        .filter((line) => line.units?.length)
        .map(async ({ units }) => {
          const unitOfLocation = await this.unitModel
            .find({ location })
            .select('_id parent');

          const unitIdsOfLocation = unitOfLocation.map(({ _id }) =>
            _id.toString(),
          );

          const rootUnitId = unitOfLocation.find(({ parent }) => !parent)?._id;
          if (rootUnitId && units.includes(rootUnitId.toString())) {
            throw new BadRequestException(
              CONTRACT_MESSAGE_KEYS.ROOT_UNIT_NOT_ALLOWED,
            );
          }

          const filterUnitId = difference(units, unitIdsOfLocation);
          if (filterUnitId.length > 0) {
            throw new BadRequestException(
              CONTRACT_MESSAGE_KEYS.UNITS_ARE_NOT_PART_OF_LOCATION,
            );
          }
        }),
    );
  }

  private async overwriteRootUnit(data: CreateContractDto) {
    const { agreementLines, location } = data;

    const indexOfAccomodationAgreementLines = agreementLines.findIndex(
      ({ type }) => AgreementLineType.ACCOMMODATION === type,
    );

    const rootUnit = await this.unitModel.findOne({ location, parent: null });

    if (!rootUnit) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.ROOT_UNIT_NOT_FOUND);
    }

    const agreementLine = agreementLines[indexOfAccomodationAgreementLines];

    agreementLines.forEach((line, index) => {
      if (index === indexOfAccomodationAgreementLines) {
        return;
      }

      line.units = [];
    });

    agreementLines[indexOfAccomodationAgreementLines] = {
      ...agreementLine,
      units: [rootUnit._id.toString()],
    };

    data.agreementLines = agreementLines;
  }

  private validateContactType(type: ContractType, contact: ContactModel) {
    switch (type) {
      case ContractType.RENTING:
      case ContractType.SERVICE:
        if (contact.contactRole !== ContactRole.DEBTOR) {
          throw new BadRequestException(
            CONTRACT_MESSAGE_KEYS.INVALID_CUSTOMER_CONTACT,
          );
        }
        break;
      case ContractType.CREDITOR:
        if (
          contact.contactRole !== ContactRole.SUPPLIER ||
          contact.supplierType !== SupplierType.RENTAL
        ) {
          throw new BadRequestException(
            CONTRACT_MESSAGE_KEYS.INVALID_SUPPLIER_CONTACT,
          );
        }
        break;
      case ContractType.SUPPLIER:
        if (
          contact.contactRole !== ContactRole.SUPPLIER ||
          contact.supplierType !== SupplierType.REGULAR
        ) {
          throw new BadRequestException(
            CONTRACT_MESSAGE_KEYS.INVALID_SUPPLIER_CONTACT,
          );
        }
        break;
      default:
        break;
    }
  }

  private validateDataWhenUpdateContract(
    contractType: ContractType,
    payload: any,
  ) {
    try {
      let data: any = {};
      switch (contractType) {
        case ContractType.RENTING: {
          data = UpdateDebtorRentingContractSchema.parse(payload);
          break;
        }

        case ContractType.CREDITOR: {
          data = UpdateCreditorRentingContractSchema.parse(payload);
          break;
        }

        case ContractType.SERVICE: {
          data = UpdateDebtorServiceContractSchema.parse(payload);
          break;
        }

        case ContractType.SUPPLIER: {
          data = UpdateSupplierContractSchema.parse(payload);
          break;
        }
        default:
          return payload;
      }

      if (data.agreementLines && data.agreementLines.length > 0) {
        data.agreementLines = this.validateUpdateAgreementLine(
          data.agreementLines,
          contractType,
        );
      }
      return data;
    } catch (error: any) {
      if (error instanceof ZodError) {
        const errorPayload = {
          message: 'Validation failed',
          errors: error.errors,
        };
        throw new BadRequestException(errorPayload);
      } else {
        throw error;
      }
    }
  }

  private validateUpdateAgreementLine(
    agreementLines: any,
    contractType: ContractType,
  ) {
    try {
      return agreementLines.map((agreementLine) => {
        if (agreementLine._id) {
          switch (contractType) {
            case ContractType.RENTING:
            case ContractType.CREDITOR:
              agreementLine =
                UpdateRentingAgreementLineSchema.parse(agreementLine);
              break;
            case ContractType.SERVICE:
              agreementLine =
                UpdateServiceAgreementLineSchema.parse(agreementLine);
              break;
            case ContractType.SUPPLIER:
              agreementLine =
                UpdateSupplierAgreementLineSchema.parse(agreementLine);
              break;
            default:
              break;
          }

          agreementLine.costLineGenerals.map((costLineGeneral) => {
            if (costLineGeneral._id) {
              return UpdateCostLineGeneralSchema.parse(costLineGeneral);
            } else {
              switch (contractType) {
                case ContractType.RENTING:
                case ContractType.CREDITOR:
                  return CreateRentingCostLineGeneralSchema.parse(
                    costLineGeneral,
                  );
                case ContractType.SERVICE:
                  return CreateServiceCostLineGeneralSchema.parse(
                    costLineGeneral,
                  );
                case ContractType.SUPPLIER:
                  return CreateSupplierCostLineGeneralSchema.parse(
                    costLineGeneral,
                  );
                default:
                  return costLineGeneral;
              }
            }
          });
          return agreementLine;
        } else {
          switch (contractType) {
            case ContractType.RENTING:
            case ContractType.CREDITOR:
              return CreateRentingAgreementLineSchema.parse(agreementLine);
            case ContractType.SERVICE:
              return CreateServiceAgreementLineSchema.parse(agreementLine);
            case ContractType.SUPPLIER:
              return CreateSupplierAgreementLineSchema.parse(agreementLine);
            default:
              return agreementLine;
          }
        }
      });
    } catch (error: any) {
      if (error instanceof ZodError) {
        const errorPayload = {
          message: 'Validation failed',
          errors: error.errors,
        };
        throw new BadRequestException(errorPayload);
      } else {
        throw error;
      }
    }
  }

  private async updateAgreementLine(
    existedContract: any,
    updatedContract: any,
    agreementLines: any[],
  ) {
    const willUpdateAgreementLines = agreementLines.filter(
      (agreementLine) => agreementLine._id,
    );

    const willCreateAgreementLines = agreementLines.filter(
      (agreementLine) => !agreementLine._id,
    );

    if (existedContract.isWholeLocation) {
      const accommodationAgreementLines = willCreateAgreementLines.filter(
        (line) => line.type === AgreementLineType.ACCOMMODATION,
      );

      if (accommodationAgreementLines.length > 1) {
        throw new BadRequestException(
          CONTRACT_MESSAGE_KEYS.CONTRACT_ONLY_HAVE_ONE_ACCOMMODATION_AGREEMENTLINE,
        );
      }

      const existAccommodationAgreementLine =
        await this.agreementLineModel.findOne({
          type: AgreementLineType.ACCOMMODATION,
          contract: existedContract._id,
        });
      if (
        accommodationAgreementLines.length > 0 &&
        existAccommodationAgreementLine
      ) {
        throw new BadRequestException(
          CONTRACT_MESSAGE_KEYS.CONTRACT_ONLY_HAVE_ONE_ACCOMMODATION_AGREEMENTLINE,
        );
      }
    }

    const allAgreementLines = [...willCreateAgreementLines];
    const existedAgreementLines = await this.agreementLineModel
      .find({
        _id: {
          $in: willUpdateAgreementLines.map(
            (line) => new mongoose.Types.ObjectId(line._id as string),
          ),
        },
      })
      .lean();

    for (const existedAgreementLine of existedAgreementLines) {
      const agreementLine = willUpdateAgreementLines.find(
        (line) => line._id === existedAgreementLine._id.toString(),
      )!;

      allAgreementLines.push({
        ...existedAgreementLine,
        ...agreementLine,
      });
    }

    const dayJsCurrentDate = dayjs().utc().startOf('day');
    const greatestAgreementLineStatus = getGreatestAgreementLinePeriod(
      allAgreementLines,
      dayJsCurrentDate,
    );

    const createAgreementLinePromises = willCreateAgreementLines.map(
      async (line: any) => {
        const { costLineGenerals, ...agreementLineData } = line;

        // Add reference to contract
        agreementLineData.contract = existedContract._id;

        const [agreementLine] = await this.agreementLineModel.create([
          agreementLineData,
        ]);
        validteAndSortPosition(costLineGenerals, ModuleNameEnum.CONTRACT);

        // Create cost line generals with reference to agreement line
        const futureGenerationDate = greatestAgreementLineStatus.isFuture
          ? calculate2PeriodsFutureGenerationDate({
              greatestPeriodType:
                greatestAgreementLineStatus.greatestPeriodType,
              startDate: greatestAgreementLineStatus.smallestStartDate,
              now: dayJsCurrentDate,
            })
          : undefined;

        // Create cost line generals with reference to agreement line
        await this.createCostLineGeneral(
          costLineGenerals,
          agreementLine,
          {
            ...existedContract,
            generatePeriod: updatedContract.generatePeriod,
            startDate: updatedContract.startDate,
            endDate: updatedContract.endDate,
          },
          dayJsCurrentDate,
          futureGenerationDate,
        );

        return agreementLine._id;
      },
    );

    const updateAgreementLinePromises = willUpdateAgreementLines.map(
      async (agreementLine: any) => {
        const { costLineGenerals, ...agreementLineData } = agreementLine;

        const foundAgreementLine = existedAgreementLines.find(
          (line) => line._id?.toString() === agreementLine._id,
        );

        if (!foundAgreementLine) {
          throw new BadRequestException(
            CONTRACT_MESSAGE_KEYS.AGREEMENT_NOT_FOUND,
          );
        }

        const willUpdateCostLineGeneral = costLineGenerals.filter(
          (costLineGeneral) => costLineGeneral._id,
        );

        const willCreateCostLineGeneral = costLineGenerals.filter(
          (costLineGeneral) => !costLineGeneral._id,
        );

        // Create cost line generals with reference to agreement line
        const futureGenerationDate = greatestAgreementLineStatus.isFuture
          ? calculate2PeriodsFutureGenerationDate({
              greatestPeriodType:
                greatestAgreementLineStatus.greatestPeriodType,
              startDate: greatestAgreementLineStatus.smallestStartDate,
              now: dayJsCurrentDate,
            })
          : undefined;

        await this.createCostLineGeneral(
          willCreateCostLineGeneral,
          foundAgreementLine,
          existedContract,
          dayJsCurrentDate,
          futureGenerationDate,
        );

        await this.updateCostLineGeneral(
          existedContract,
          updatedContract,
          foundAgreementLine,
          willUpdateCostLineGeneral,
          dayJsCurrentDate,
          greatestAgreementLineStatus,
        );

        await this.agreementLineModel.updateOne(
          { _id: agreementLine._id },
          { ...agreementLineData },
        );
      },
    );

    const createdAgreementLineIds = await Promise.all(
      createAgreementLinePromises,
    );

    await Promise.all(updateAgreementLinePromises);

    return { createdAgreementLineIds };
  }

  private async updateCostLineGeneral(
    existedContract: any,
    updatedContract: any,
    agreementLine: any,
    costLineGenerals: any[],
    dayJsCurrentDate: dayjs.Dayjs,
    greatestAgreementLineStatus: any,
  ) {
    costLineGenerals = costLineGenerals.map((costLineGeneral) => ({
      ...costLineGeneral,
      startDate: dayjs(costLineGeneral.startDate).utc(),
      endDate: costLineGeneral.endDate
        ? dayjs(costLineGeneral.endDate).utc()
        : costLineGeneral.endDate,
    }));

    const exitedCostlinegenerals = await this.costLineGeneralModel
      .find({
        agreementLine: { $in: existedContract.agreementLines },
      })
      .lean();

    const exitedCostlinegeneralIds = exitedCostlinegenerals.map(
      (exitedCostlinegeneral) => exitedCostlinegeneral._id.toString(),
    );

    const payloadCostlinegeneralIds = costLineGenerals.map(
      (costLineGeneral) => costLineGeneral._id,
    );

    const isValid = payloadCostlinegeneralIds.every((v) =>
      exitedCostlinegeneralIds.includes(v),
    );

    if (!isValid) {
      throw new BadRequestException(
        CONTRACT_MESSAGE_KEYS.SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT,
      );
    }

    const updateCostLineGeneralPromise = costLineGenerals.map(
      async (costLineGeneral) => {
        const { _id, ...rest } = costLineGeneral;

        if (existedContract.isGenerateCostLine) {
          const existedCostLineGeneral = exitedCostlinegenerals.find((i) =>
            i._id.equals(_id.toString()),
          )!;

          const futureGenerationDate = getFutureGenerationDate(
            greatestAgreementLineStatus,
            dayJsCurrentDate,
          );

          const costLineGeneralUpdatedFields = getCostLineGeneralUpdatedFields({
            existedContract: existedContract,
            updatedContract: updatedContract,
            existedCostLineGeneral,
            updatedCostLineGeneral: costLineGeneral,
          });

          if (costLineGeneralUpdatedFields.length) {
            costLineGeneral.costLines =
              await this.costLineService.updateCostLines({
                existedCostLineGeneral: existedCostLineGeneral,
                updatedCostLineGeneral: {
                  ...costLineGeneral,
                  costType: existedCostLineGeneral.costType,
                },
                agreementLine,
                updatedContract: {
                  ...existedContract,
                  generatePeriod:
                    updatedContract.generatePeriod ??
                    existedContract.generatePeriod,
                  // ...(costLineGeneralUpdatedFields.includes(
                  //   'generatePeriod',
                  // ) && { generatePeriod: updatedContract.generatePeriod }),
                },
                dayJsCurrentDate,
                futureGenerationDate,
                costLineGeneralUpdatedFields,
              });
          }
        }

        return this.costLineGeneralModel.findOneAndUpdate(
          { _id: _id },
          { ...rest },
          { new: true },
        );
      },
    );

    return await Promise.all(updateCostLineGeneralPromise);
  }

  private async createCostLineGeneral(
    createCostLineGenerals: any[],
    agreementLine: any,
    contract: any,
    dayJsCurrentDate: dayjs.Dayjs,
    futureGenerationDate?: Date,
  ) {
    const noFutureGenerationDate =
      !futureGenerationDate ||
      dayJsCurrentDate.isSameOrAfter(futureGenerationDate);
    const createCostLineGeneralData = createCostLineGenerals.map(
      async (costLineGeneral: any) => {
        return {
          ...costLineGeneral,
          agreementLine: agreementLine._id,
          endDate: costLineGeneral.endDate || contract.endDate,
          futureGenerationDate: noFutureGenerationDate
            ? calculateNextFutureGenerationDate({
                period: agreementLine.period,
                generatePeriod: contract.generatePeriod,
                startDate: dayjs(costLineGeneral.startDate),
                now: dayJsCurrentDate,
              })
            : futureGenerationDate,
        };
      },
    );

    const createdCostLineGenerals = await this.costLineGeneralModel.insertMany(
      await Promise.all(createCostLineGeneralData),
    );

    if (
      contract.isGenerateCostLine &&
      (!futureGenerationDate ||
        dayJsCurrentDate.isSameOrAfter(futureGenerationDate))
    ) {
      const costLinesPromises = createdCostLineGenerals.map(
        async (costLineGeneral) => {
          return await this.costLineService.createCostLines({
            costLineGeneral,
            agreementLine,
            contract,
          });
        },
      );

      await Promise.all(costLinesPromises);
    }

    // Update reference of agreement line
    await this.agreementLineModel.findByIdAndUpdate(agreementLine._id, {
      $push: {
        costLineGenerals: {
          $each: createdCostLineGenerals.map((costLine) => costLine._id),
        },
      },
    });
  }

  private async validateReadPermissionByContractType(
    userId: string,
    type: ContractType,
  ) {
    const roles = getReadRolesByContractType(type);
    if (roles.length === 0) {
      throw new ForbiddenException();
    }

    const isValid = await this.tenantUserService.verifyRoles(userId, roles);

    if (!isValid) {
      throw new ForbiddenException();
    }
  }

  private async validateManagePermissionByContractType(
    userId: string,
    type: ContractType,
  ) {
    const role = getManageRoleByContractType(type);
    if (!role) {
      throw new ForbiddenException();
    }

    const isValid = await this.tenantUserService.verifyRoles(userId, [role]);

    if (!isValid) {
      throw new ForbiddenException();
    }
  }

  // #endregion
}

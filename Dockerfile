FROM node:20-alpine AS builder
# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile
COPY . .
RUN yarn build

FROM node:20-alpine AS final
ENV NODE_ENV=production
USER node
# Create app directory
WORKDIR /usr/src/app
# Install app dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile
COPY --from=builder /usr/src/app/dist ./dist
CMD [ "node", "dist/main.js" ]
EXPOSE 80
import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { DATE_FORMAT_HYPHEN } from '~/constants/app.constant';
import { LocationModel } from '~/modules/location/location.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { AgreementLineType, ContractType } from '~/shared/enums/contract.enum';
import {
  // testClearCollections,
  TestDBModule,
} from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockContract } from '~/test/mocks/contract.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockStatsOccupant } from '~/test/mocks/stats-occupant.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import { StatsOccupantModel } from '../stats-occupant.model';
import { StatsOccupantService } from '../stats-occupant.service';

const unitId2 = new ObjectId();
const maxOccupantsUnit2 = 10;
const unitId3 = new ObjectId();
const maxOccupantsUnit3 = 10;

const locationMaxOccupants = 30;

const costlineGeneralId2 = new ObjectId();

const agreementLineId2 = new ObjectId();

describe('StatsOccupantService', () => {
  let service: StatsOccupantService;
  const creditorContractId = new ObjectId();
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        StatsOccupantService,
        ...testInjectModel([LocationModel, StatsOccupantModel, UnitModel]),
      ],
    }).compile();

    service = module.get(StatsOccupantService);

    await Promise.all([
      initMockUnit({ isRoot: false, name: 'Unit 1' }),
      initMockUnit({
        _id: unitId2,
        name: 'Unit 2',
        location: mockLocationData._id,
        maxOccupants: maxOccupantsUnit2,
        isRoot: false,
      }),
      initMockUnit({
        _id: unitId3,
        name: 'Unit 3',
        location: mockLocationData._id,
        maxOccupants: maxOccupantsUnit3,
        isRoot: false,
      }),
      initMockCostlineGeneral(),
      initMockCostlineGeneral({
        _id: costlineGeneralId2,
        unit: unitId2,
      }),
      initMockAgreementLine({
        costLineGenerals: [mockCostlineGeneralData._id],
        type: AgreementLineType.ACCOMMODATION,
      }),
      initMockAgreementLine({
        _id: agreementLineId2,
        costLineGenerals: [costlineGeneralId2],
        type: AgreementLineType.SERVICE,
      }),
      initMockContract({
        _id: creditorContractId,
        type: ContractType.CREDITOR,
      }),
      initMockContract({
        agreementLines: [mockAgreementLineData._id, agreementLineId2],
        location: mockLocationData._id,
        type: ContractType.RENTING,
        isWholeLocation: false,
      }),
      initMockStatsOccupant({ creditorContracts: [creditorContractId] }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('calculateHiredLocations', () => {
    it('should return array empty when no data found', async () => {
      await service['locationModel'].deleteMany({});
      const result = await service.calculateHiredLocations({});
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    it('should return result when location and contract exist', async () => {
      const locationId = new ObjectId();
      await Promise.all([
        initMockLocation({
          _id: locationId,
        }),
        initMockContract({
          _id: new ObjectId(),
          type: ContractType.RENTING,
          location: locationId,
          agreementLines: [],
        }),
      ]);

      const result = await service.calculateHiredLocations({});
      expect(result).toBeDefined();
    });

    it('should return result when location, unit and contract exist', async () => {
      const locationId = new ObjectId();
      await Promise.all([
        initMockLocation({
          _id: locationId,
        }),
        initMockUnit({
          _id: new ObjectId(),
          location: locationId,
        }),
        initMockContract({
          _id: new ObjectId(),
          type: ContractType.RENTING,
          location: locationId,
        }),
      ]);

      const result = await service.calculateHiredLocations({});
      expect(result).toBeDefined();
    });

    it('should return result when only location exists', async () => {
      await initMockLocation({
        _id: new ObjectId(),
      });

      const result = await service.calculateHiredLocations({});
      expect(result).toBeDefined();
    });

    it('should return hired locations', async () => {
      await initMockLocation({
        maxOccupants: locationMaxOccupants,
      });

      const result = await service.calculateHiredLocations({});
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('exportListOccupation', () => {
    it('should export occupation list with correct structure', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.exportListOccupation({});
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
      expect(Array.isArray(result.data)).toBe(true);
      expect(Array.isArray(result.header)).toBe(true);
      expect(typeof result.fileName).toBe('string');
      const currentDate = dayjs()
        .utc()
        .startOf('day')
        .format(DATE_FORMAT_HYPHEN);
      expect(result.fileName).toEqual(`list-occupations-${currentDate}.csv`);
      if (result.data.length > 0) {
        expect(result.data[0]).toHaveProperty('dateOfReport');
        expect(result.data[0]).toHaveProperty('costCenter');
        expect(result.data[0]).toHaveProperty('location');
        expect(result.data[0]).toHaveProperty('team');
        expect(result.data[0]).toHaveProperty('maxCount');
        expect(result.data[0]).toHaveProperty('hiredCount');
        expect(result.data[0]).toHaveProperty('emptyCount');
      }
    });
  });

  describe('getListOccupation', () => {
    it('should return stats summary', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.getListOccupation({});
      expect(Array.isArray(result.items)).toBe(true);
      const firstItem = result.items[0];
      const emptyCount = maxOccupantsUnit2 + maxOccupantsUnit3;
      expect(firstItem.emptyCount).toEqual(emptyCount);
      expect(firstItem.hiredCount).toEqual(locationMaxOccupants - emptyCount);
      expect(firstItem.unHiredUnits).toBeDefined();
      expect(firstItem.unHiredUnits.length).toStrictEqual(2);
      expect(result.totalMaxCount).toBe(locationMaxOccupants);
      expect(result.totalHiredCount).toBe(locationMaxOccupants - emptyCount);
      expect(result.totalEmptyCount).toBe(emptyCount);
    });

    it('should return stats summary and calculate Hired Locations', async () => {
      const calculateHiredLocationsSpy = jest.spyOn(
        service,
        'calculateHiredLocations',
      );

      await service['statsOccupantModel'].deleteMany();

      const result = await service.getListOccupation({});
      expect(result).toBeDefined();
      expect(calculateHiredLocationsSpy).toHaveBeenCalled();
    });
  });

  describe('getListVacancies', () => {
    it('should return vacancies', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });

      await service['statsOccupantModel'].updateOne(
        { location: mockLocationData._id },
        { markNeedToUpdate: true },
      );

      const result = await service.getListVacancies({});
      expect(Array.isArray(result)).toBe(true);
      const firstItem = result[0];
      expect(firstItem.emptyCount).toEqual(
        maxOccupantsUnit2 + maxOccupantsUnit3,
      );
    });

    it('should return defined result when no statsOccupant data exists', async () => {
      await service['statsOccupantModel'].deleteMany();
      const result = await service.getListVacancies({});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result[0]).toHaveProperty('locationInfo');
      expect(result[0]).toHaveProperty('teamInfo');
      expect(result[0]).toHaveProperty('unHiredUnits');
      expect(result[0]).toHaveProperty('emptyCount');
    });

    it('should return empty array when no vacancies found', async () => {
      jest.spyOn(service['statsOccupantModel'], 'aggregate').mockReturnValue({
        exec: jest.fn().mockResolvedValue([]),
      } as any);

      const result = await service.getListVacancies({});
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
      jest.restoreAllMocks();
    });
  });

  describe('exportListVacancies', () => {
    it('should export vacancies list with correct structure', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.exportListVacancies({});
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
      expect(Array.isArray(result.data)).toBe(true);
      expect(Array.isArray(result.header)).toBe(true);
      expect(typeof result.fileName).toBe('string');
      if (result.data.length > 0) {
        expect(result.data[0]).toHaveProperty('dateOfReport');
        expect(result.data[0]).toHaveProperty('locationInfo');
        expect(result.data[0]).toHaveProperty('teamInfo');
        expect(result.data[0]).toHaveProperty('unit');
        expect(result.data[0]).toHaveProperty('emptyCount');
      }
    });
  });

  describe('recalculateStatsOccupantsMarkNeedToUpdate', () => {
    it('should mark stats occupants as needing update', async () => {
      const payload = {
        locationId: mockLocationData._id,
      };
      const result =
        await service.recalculateStatsOccupantsMarkNeedToUpdate(payload);
      expect(result).toBeDefined();
    });

    it('should create new stats occupants when no existing data found', async () => {
      const newLocationId = new ObjectId();
      await initMockLocation({
        _id: newLocationId,
        maxOccupants: 50,
        isActive: true,
        isService: false,
        fullAddress: 'Test Address 123',
      });

      const payload = { locationId: newLocationId };
      const result =
        await service.recalculateStatsOccupantsMarkNeedToUpdate(payload);

      expect(result).toBeDefined();
      expect(result.message).toContain(
        `StatsOccupants with locationId ${newLocationId} marked as need to update`,
      );
    });

    it('should not throw error if locationId is undefined', async () => {
      const payload = {};
      await expect(
        service.recalculateStatsOccupantsMarkNeedToUpdate(payload),
      ).rejects.toThrow('Location ID is required');
    });

    it('should not throw error if location not found', async () => {
      await initMockStatsOccupant({ creditorContracts: [creditorContractId] });

      const newLocationId = new ObjectId();
      const payload = { locationId: newLocationId };
      await expect(
        service.recalculateStatsOccupantsMarkNeedToUpdate(payload),
      ).rejects.toThrow(`Location with ID ${newLocationId} not found`);
    });

    it('should return message StatsOccupants with location marked as need to update', async () => {
      const locationId = new ObjectId();
      await service['statsOccupantModel'].deleteMany({});
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: locationId,
      });
    });
  });

  describe('groupAndSeprateUnitBelongToRootUnit', () => {
    it('should return acc when newKey is empty', () => {
      const units = [{ parent: 1, name: 'Unit A' }];
      const allUnitsExceptRoot = [{ _id: 1, name: '' }];

      const result = service['groupAndSeprateUnitBelongToRootUnit'](
        units,
        allUnitsExceptRoot,
      );
      expect(result).toEqual({});
    });

    it('should handle belongToRootUnit adding and skipping units', () => {
      const units = [
        { parent: 1, name: 'Group1-Unit' },
        { parent: 2, name: 'Group1' },
        { parent: 2, name: 'Unit X' },
      ];

      const allUnitsExceptRoot = [{ _id: 1, name: 'Group1' }];

      const result = service['groupAndSeprateUnitBelongToRootUnit'](
        units,
        allUnitsExceptRoot,
      );

      expect(result).toHaveProperty('Group1');
      expect(Array.isArray(result['Group1'])).toBe(true);
      expect(result).toHaveProperty('Unit X');
      expect(result['Unit X']).toEqual({ parent: 2, name: 'Unit X' });
      expect(result).not.toHaveProperty('belongToRootUnit');
    });
  });

  describe('isAllChildenHired', () => {
    const unit1Id = new ObjectId();
    const unit2Id = new ObjectId();
    const unit3Id = new ObjectId();

    beforeEach(async () => {
      await service['unitModel'].deleteMany({});

      await Promise.all([
        initMockUnit({ name: 'UP', parent: null }),
        initMockUnit({ _id: unit1Id, name: 'U1', parent: mockUnitData._id }),
        initMockUnit({ _id: unit2Id, name: 'U2', parent: mockUnitData._id }),
        initMockUnit({ _id: unit3Id, name: 'U3', parent: mockUnitData._id }),
      ]);
    });

    it('should return true when every child is in hiredUnits', async () => {
      const parentUnit = mockUnitData;
      const hiredUnits = [{ _id: unit1Id }, { _id: unit2Id }, { _id: unit3Id }];

      const result = await service['isAllChildenHired'](parentUnit, hiredUnits);
      expect(result).toBe(true);
    });

    it('should return false when at least one child is missing in hiredUnits', async () => {
      const parentUnit = mockUnitData;
      const hiredUnits = [{ _id: unit1Id }, { _id: unit2Id }];

      const result = await service['isAllChildenHired'](parentUnit, hiredUnits);
      expect(result).toBe(false);
    });
  });
});

import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { createTransport } from 'nodemailer';

import { EmailTemplateService } from '~/modules/email-template/email-template.service';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { getModelToken } from '~/transformers/model.transformer';

import { MyLogger } from '../../logger/logger.service';
import { SendEmailDto } from '../dtos/send-email.dto';
import { EmailService } from '../email.service';

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(),
}));

// Mock @azure/identity
jest.mock('@azure/identity', () => ({
  ClientSecretCredential: jest.fn().mockImplementation(() => ({
    getToken: jest.fn().mockResolvedValue({
      token: 'mock-access-token',
      expiresOnTimestamp: Date.now() + 3600000, // 1 hour from now
    }),
  })),
}));

// Mock renderTextTemplate
jest.mock('~/utils/render-text-template.util', () => ({
  renderTextTemplate: jest.fn((data, template) => {
    // Simple template replacement for testing
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }),
}));

describe('EmailService', () => {
  let service: EmailService;
  let configService: ConfigService;
  let logger: MyLogger;
  let emailTemplateService: EmailTemplateService;
  let mockTransporter: any;

  const mockTenantModel = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    // Mock transporter
    mockTransporter = {
      sendMail: jest.fn(),
      verify: jest.fn(),
    };

    (createTransport as jest.Mock).mockReturnValue(mockTransporter);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: getModelToken(TenantModel.name),
          useValue: mockTenantModel,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config = {
                'email.office365.tenantId': 'mock-tenant-id',
                'email.office365.clientId': 'mock-client-id',
                'email.office365.clientSecret': 'mock-client-secret',
                'email.office365.userId': '<EMAIL>',
                'app.lentoErrorReceiverEmail': '<EMAIL>',
              };
              return config[key];
            }),
          },
        },
        {
          provide: MyLogger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
        {
          provide: EmailTemplateService,
          useValue: {
            getEmailTemplateByName: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    configService = module.get<ConfigService>(ConfigService);
    logger = module.get<MyLogger>(MyLogger);
    emailTemplateService =
      module.get<EmailTemplateService>(EmailTemplateService);

    // Mock onModuleInit to avoid actual initialization
    jest
      .spyOn(service as any, 'initializeOffice365SMTP')
      .mockResolvedValue(undefined);

    // Set the mock transporter to the service instance
    (service as any).transporter = mockTransporter;

    // Mock credential
    (service as any).credential = {
      getToken: jest.fn().mockResolvedValue({
        token: 'mock-access-token',
        expiresOnTimestamp: Date.now() + 3600000,
      }),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should initialize Office 365 SMTP on module init', async () => {
      const initializeSpy = jest.spyOn(
        service as any,
        'initializeOffice365SMTP',
      );

      await service.onModuleInit();

      expect(initializeSpy).toHaveBeenCalled();
    });
  });

  describe('sendEmail', () => {
    it('should send email successfully', async () => {
      const mockResponse = { messageId: 'test-message-id' };
      mockTransporter.sendMail.mockResolvedValue(mockResponse);

      const emailParams: SendEmailDto = {
        to: ['<EMAIL>'],
        subject: 'Test Subject',
        text: 'Test content',
      };

      const result = await service.sendEmail(emailParams);

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        cc: '',
        bcc: '',
        subject: 'Test Subject',
        html: '',
        text: 'Test content',
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle array of recipients', async () => {
      const mockResponse = { messageId: 'test-message-id' };
      mockTransporter.sendMail.mockResolvedValue(mockResponse);

      const emailParams: SendEmailDto = {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: 'Test Subject',
        text: 'Test content',
      };

      await service.sendEmail(emailParams);

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>, <EMAIL>',
        cc: '',
        bcc: '',
        subject: 'Test Subject',
        html: '',
        text: 'Test content',
      });
    });

    it('should handle authentication error and retry', async () => {
      const mockResponse = { messageId: 'test-message-id' };

      // First call fails with auth error, second succeeds
      mockTransporter.sendMail
        .mockRejectedValueOnce({
          code: 'EAUTH',
          message: 'Authentication unsuccessful',
        })
        .mockResolvedValueOnce(mockResponse);

      const emailParams: SendEmailDto = {
        to: ['<EMAIL>'],
        subject: 'Test Subject',
        text: 'Test content',
      };

      const result = await service.sendEmail(emailParams);

      expect(mockTransporter.sendMail).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockResponse);
    });

    it('should throw error for non-authentication failures', async () => {
      const error = new Error('Network error');
      mockTransporter.sendMail.mockRejectedValue(error);

      const emailParams: SendEmailDto = {
        to: ['<EMAIL>'],
        subject: 'Test Subject',
        text: 'Test content',
      };

      await expect(service.sendEmail(emailParams)).rejects.toThrow(
        'Network error',
      );
    });
  });

  describe('sendEmailWithTemplate', () => {
    it('should send email with template successfully', async () => {
      const mockTemplate = {
        to: ['<EMAIL>'],
        cc: ['<EMAIL>'],
        bcc: ['<EMAIL>'],
        subject: 'Hello {{name}}',
        html: '<h1>Hello {{name}}</h1>',
        text: 'Hello {{name}}',
      };

      const mockResponse = { messageId: 'test-message-id' };

      (
        emailTemplateService.getEmailTemplateByName as jest.Mock
      ).mockResolvedValue(mockTemplate);
      mockTransporter.sendMail.mockResolvedValue(mockResponse);

      const result = await service.sendEmailWithTemplate('test-template', {
        name: 'John',
      });

      expect(emailTemplateService.getEmailTemplateByName).toHaveBeenCalledWith(
        'test-template',
      );
      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        cc: '<EMAIL>',
        bcc: '<EMAIL>',
        subject: 'Hello John',
        html: '<h1>Hello John</h1>',
        text: 'Hello John',
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle template with empty recipients', async () => {
      const mockTemplate = {
        to: [],
        cc: [],
        bcc: [],
        subject: 'Test Subject',
        html: '<h1>Test</h1>',
        text: 'Test',
      };

      const mockResponse = { messageId: 'test-message-id' };

      (
        emailTemplateService.getEmailTemplateByName as jest.Mock
      ).mockResolvedValue(mockTemplate);
      mockTransporter.sendMail.mockResolvedValue(mockResponse);

      await service.sendEmailWithTemplate('test-template', {});

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '',
        cc: '',
        bcc: '',
        subject: 'Test Subject',
        html: '<h1>Test</h1>',
        text: 'Test',
      });
    });
  });

  describe('sendErrorEmailToLento', () => {
    it('should handle missing template', async () => {
      (
        emailTemplateService.getEmailTemplateByName as jest.Mock
      ).mockResolvedValue(null);

      const errorData = {
        PROCESS_NAME: 'Test Process',
        TENANT: 'Test Tenant',
        ERROR_MESSAGE: 'Test error',
        TIME: new Date().toISOString(),
      };
      await service.sendErrorEmailToLento(errorData);

      expect(logger.error).toHaveBeenCalledWith(
        'Email template not found: error_email_send_to_lento',
      );
      expect(mockTransporter.sendMail).not.toHaveBeenCalled();
    });

    it('should handle missing receiver email config', async () => {
      const mockTemplate = {
        subject: 'Error Report',
        html: '<h1>Error</h1>',
      };

      (
        emailTemplateService.getEmailTemplateByName as jest.Mock
      ).mockResolvedValue(mockTemplate);
      (configService.get as jest.Mock).mockReturnValue(null);

      const errorData = {
        PROCESS_NAME: 'Test Process',
        TENANT: 'Test Tenant',
        ERROR_MESSAGE: 'Test error',
        TIME: new Date().toISOString(),
      };
      await service.sendErrorEmailToLento(errorData);

      expect(logger.error).toHaveBeenCalledWith(
        'Lento error receiver email is not configured',
      );
      expect(mockTransporter.sendMail).not.toHaveBeenCalled();
    });
  });

  describe('getUserEmail', () => {
    it('should return user email from config', async () => {
      const result = await service.getUserEmail();

      expect(result).toBe('<EMAIL>');
    });

    it('should handle config error', async () => {
      (configService.get as jest.Mock).mockImplementation(() => {
        throw new Error('Config error');
      });

      await expect(service.getUserEmail()).rejects.toThrow('Config error');
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to get user email:',
        expect.any(Error),
      );
    });
  });

  describe('onModuleDestroy', () => {
    it('should cleanup resources on module destroy', () => {
      // Set up a mock interval
      (service as any).refreshTokenInterval = setTimeout(() => {}, 1000);

      service.onModuleDestroy();

      expect(logger.log).toHaveBeenCalledWith(
        'Email service cleanup completed',
      );
    });
  });
});

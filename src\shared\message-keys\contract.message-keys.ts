export const CONTRACT_MESSAGE_KEYS = {
  NOT_FOUND: 'contract.find.not_found',
  UNITS_ARE_NOT_PART_OF_LOCATION:
    'contract.form.units_are_not_part_of_location',
  ROOT_UNIT_NOT_ALLOWED: 'contract.form.root_unit_not_allowed',
  INVALID_CUSTOMER_CONTACT: 'contract.form.invalid_customer_contact',
  INVALID_SUPPLIER_CONTACT: 'contract.form.invalid_supplier_contact',
  AGREEMENT_NOT_FOUND: 'contract.form.agreement_not_found',
  CONTRACT_ONLY_HAVE_ONE_ACCOMMODATION_AGREEMENTLINE:
    'contract.update.contract_only_have_one_accommodation_agreementline',
  SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT:
    'contract.update.some_costline_general_are_not_in_contract',
  START_DATE_CANNOT_BE_CHANGED_BEFORE_60_DAYS:
    'contract.update.start_date_cannot_be_changed_before_60_days',
  INVALID_CONTRACT_TYPE: 'contract.form.invalid_contract_type',
};
